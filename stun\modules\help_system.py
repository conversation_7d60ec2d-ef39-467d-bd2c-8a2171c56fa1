
import re
from typing import Dict, List, Optional, Any, Mapping, Callable

import discord
from discord import Embed, SelectOption, ButtonStyle
from discord.ext.commands import Cog, Command, Group
from discord.ui import View, Select, Button

from .styles import colors, emojis
from .helpers import EvelinaContext


class HelpView(View):
    """Main interactive help view with category dropdown and navigation"""
    
    def __init__(self, ctx: EvelinaContext, mapping: Mapping[Cog | None, List[Command[Any, Callable[..., Any], Any]]], main_embed: Embed):
        super().__init__(timeout=300)
        self.ctx = ctx
        self.mapping = mapping
        self.main_embed = main_embed
        self.current_embed = main_embed
        self.current_view_type = "main"  # main, category, command
        self.current_category = None
        
        # Add category dropdown
        self.add_item(CategorySelect(ctx, mapping))

        # Add navigation buttons
        self.add_item(SearchButton(ctx, mapping))
        self.add_item(HomeButton())
        self.add_item(DeleteButton())
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if the user can interact with this view"""
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} {interaction.user.mention}: You are not the **author** of this help menu"
                ),
                ephemeral=True
            )
            return False
        return True
    
    async def update_view(self, interaction: discord.Interaction, embed: Embed = None, view: View = None):
        """Update the message with new embed and view"""
        try:
            if embed is None:
                embed = self.current_embed
            if view is None:
                view = self
                
            await interaction.response.edit_message(embed=embed, view=view)
        except discord.InteractionResponded:
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=view)
        except Exception as e:
            await interaction.followup.send(
                embed=Embed(
                    color=colors.ERROR,
                    description=f"{emojis.DENY} An error occurred: {str(e)}"
                ),
                ephemeral=True
            )
    
    def get_cog_emoji(self, cog_name: str) -> str:
        """Get appropriate emoji for cog category"""
        emoji_map = {
            "info": emojis.INFO,
            "utility": "🔧",
            "moderation": "🛡️",
            "economy": emojis.ECONOMY,
            "music": "🎵",
            "fun": "🎮",
            "config": "⚙️",
            "automod": "🤖",
            "logging": "📝",
            "tickets": "🎫",
            "giveaway": "🎉",
            "suggestion": "💡",
            "voicemaster": "🔊",
            "lastfm": emojis.LASTFM,
            "spotify": emojis.SPOTIFY,
            "social": "📱",
            "selfbot": "👤",
            "developer": "👨‍💻",
            "store": "🛒"
        }
        return emoji_map.get(cog_name.lower(), "📂")


class CategorySelect(Select):
    """Dropdown menu for selecting command categories"""
    
    def __init__(self, ctx: EvelinaContext, mapping: Mapping[Cog | None, List[Command[Any, Callable[..., Any], Any]]]):
        self.ctx = ctx
        self.mapping = mapping
        
        # Create options for each cog with commands
        options = []
        
        # Add home option
        options.append(SelectOption(
            label="🏠 Home",
            value="home",
            description="Return to the main help page",
            emoji="🏠"
        ))
        
        # Process cogs and create options
        for cog, commands in mapping.items():
            if cog is None:
                continue
                
            # Filter out hidden commands and check permissions
            visible_commands = []
            for cmd in commands:
                if cmd.hidden:
                    continue
                    
                # Check if user has permission to see this command
                cog_name = cmd.cog_name
                if cog_name and cog_name.lower() in ["owner", "jishaku", "auth", "helper"]:
                    if ctx.author.id not in ctx.bot.owner_ids:
                        continue
                        
                visible_commands.append(cmd)
            
            if not visible_commands:
                continue
                
            # Get cog info
            cog_name = cog.qualified_name if hasattr(cog, 'qualified_name') else str(cog)
            description = getattr(cog, 'description', f"{len(visible_commands)} commands")
            
            # Truncate description if too long
            if len(description) > 100:
                description = description[:97] + "..."
            
            # Get emoji for this cog
            emoji = self.get_cog_emoji(cog_name)
            
            options.append(SelectOption(
                label=f"{cog_name.title()}",
                value=f"cog_{cog_name.lower()}",
                description=description,
                emoji=emoji
            ))
        
        # Limit to 25 options (Discord limit)
        if len(options) > 25:
            options = options[:25]
        
        super().__init__(
            placeholder="📂 Select a command category...",
            options=options,
            min_values=1,
            max_values=1
        )
    
    def get_cog_emoji(self, cog_name: str) -> str:
        """Get appropriate emoji for cog category"""
        emoji_map = {
            "info": emojis.INFO,
            "utility": "🔧",
            "moderation": "🛡️",
            "economy": emojis.ECONOMY,
            "music": "🎵",
            "fun": "🎮",
            "config": "⚙️",
            "automod": "🤖",
            "logging": "📝",
            "tickets": "🎫",
            "giveaway": "🎉",
            "suggestion": "💡",
            "voicemaster": "🔊",
            "lastfm": emojis.LASTFM,
            "spotify": emojis.SPOTIFY,
            "social": "📱",
            "selfbot": "👤",
            "developer": "👨‍💻",
            "store": "🛒"
        }
        return emoji_map.get(cog_name.lower(), "📂")
    
    async def callback(self, interaction: discord.Interaction):
        """Handle category selection"""
        view: HelpView = self.view
        selected_value = self.values[0]
        
        if selected_value == "home":
            # Return to main help page
            view.current_embed = view.main_embed
            view.current_view_type = "main"
            view.current_category = None
            await view.update_view(interaction)
            return
        
        # Extract cog name from value
        cog_name = selected_value.replace("cog_", "")
        
        # Find the selected cog
        selected_cog = None
        for cog in view.mapping.keys():
            if cog and cog.qualified_name.lower() == cog_name:
                selected_cog = cog
                break
        
        if not selected_cog:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.ERROR,
                    description=f"{emojis.DENY} Category not found!"
                ),
                ephemeral=True
            )
            return
        
        # Create category embed
        await self.show_category(interaction, view, selected_cog)
    
    async def show_category(self, interaction: discord.Interaction, view: HelpView, cog: Cog):
        """Show commands for a specific category"""
        commands = view.mapping[cog]
        
        # Filter visible commands
        visible_commands = []
        for cmd in commands:
            if cmd.hidden:
                continue
                
            # Check permissions
            cog_name = cmd.cog_name
            if cog_name and cog_name.lower() in ["owner", "jishaku", "auth", "helper"]:
                if view.ctx.author.id not in view.ctx.bot.owner_ids:
                    continue
                    
            visible_commands.append(cmd)
        
        if not visible_commands:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} No commands available in this category!"
                ),
                ephemeral=True
            )
            return
        
        # Create category embed
        cog_name = cog.qualified_name if hasattr(cog, 'qualified_name') else str(cog)
        emoji = self.get_cog_emoji(cog_name)
        
        embed = Embed(
            color=colors.NEUTRAL,
            title=f"{emoji} {cog_name.title()} Commands",
            description=getattr(cog, 'description', f"Commands in the {cog_name} category")
        )
        
        embed.set_author(
            name=view.ctx.bot.user.name,
            icon_url=view.ctx.bot.user.avatar.url if view.ctx.bot.user.avatar else view.ctx.bot.user.default_avatar.url
        )
        
        # Add commands to embed (paginated if too many)
        command_list = []
        for cmd in sorted(visible_commands, key=lambda x: x.qualified_name.lower()):
            # Format command entry
            cmd_desc = cmd.description or cmd.help or "No description"
            if len(cmd_desc) > 50:
                cmd_desc = cmd_desc[:47] + "..."

            # Add cooldown info if available
            cooldown_info = ""
            if cmd.cooldown:
                cooldown_str = str(cmd.cooldown)
                match = re.search(r'per:\s*(\d+(\.\d+)?)', cooldown_str)
                if match:
                    seconds = float(match.group(1))
                    cooldown_info = f" • {int(seconds)}s cooldown" if seconds.is_integer() else f" • {seconds}s cooldown"

            command_list.append(f"`{view.ctx.clean_prefix}{cmd.qualified_name}` - {cmd_desc}{cooldown_info}")
        
        # Split into pages if too many commands
        if len(command_list) > 10:
            # Create paginated view
            view.current_commands = visible_commands
            view.current_category = cog
            view.current_page = 0
            view.current_view_type = "category"
            
            # Show first page
            page_commands = command_list[:10]
            embed.add_field(
                name=f"Commands (Page 1/{(len(command_list) + 9) // 10})",
                value="\n".join(page_commands),
                inline=False
            )
            
            # Add pagination buttons
            new_view = CategoryView(view.ctx, view, cog, visible_commands, command_list)
            await view.update_view(interaction, embed, new_view)
        else:
            # Show all commands on one page
            embed.add_field(
                name=f"Commands ({len(command_list)})",
                value="\n".join(command_list),
                inline=False
            )

            # Add category statistics
            total_commands = len(visible_commands)
            commands_with_cooldown = len([cmd for cmd in visible_commands if cmd.cooldown])

            embed.add_field(
                name="Category Statistics",
                value=(
                    f"📊 **Total Commands:** {total_commands}\n"
                    f"⏱️ **With Cooldowns:** {commands_with_cooldown}\n"
                    f"🔧 **Module:** {cog_name.lower()}.py"
                ),
                inline=True
            )

            embed.add_field(
                name="Usage",
                value=f"Use `{view.ctx.clean_prefix}help <command>` for detailed help on a specific command.",
                inline=False
            )

            embed.set_footer(
                text=f"Requested by {view.ctx.author.display_name} • {len(visible_commands)} commands",
                icon_url=view.ctx.author.avatar.url if view.ctx.author.avatar else view.ctx.author.default_avatar.url
            )

            view.current_embed = embed
            view.current_view_type = "category"
            view.current_category = cog
            await view.update_view(interaction, embed)


class CategoryView(View):
    """View for paginated category command listing"""
    
    def __init__(self, ctx: EvelinaContext, parent_view: HelpView, cog: Cog, commands: List[Command], command_list: List[str]):
        super().__init__(timeout=300)
        self.ctx = ctx
        self.parent_view = parent_view
        self.cog = cog
        self.commands = commands
        self.command_list = command_list
        self.current_page = 0
        self.total_pages = (len(command_list) + 9) // 10
        
        # Add navigation buttons
        if self.total_pages > 1:
            self.add_item(PreviousPageButton())
            self.add_item(NextPageButton())
        
        self.add_item(BackToCategoriesButton())
        self.add_item(HomeButton())
        self.add_item(DeleteButton())
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if the user can interact with this view"""
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} {interaction.user.mention}: You are not the **author** of this help menu"
                ),
                ephemeral=True
            )
            return False
        return True


class HomeButton(Button):
    """Button to return to main help page"""
    
    def __init__(self):
        super().__init__(
            style=ButtonStyle.secondary,
            emoji="🏠",
            label="Home"
        )
    
    async def callback(self, interaction: discord.Interaction):
        view: HelpView = self.view
        if hasattr(view, 'parent_view'):
            view = view.parent_view
            
        view.current_embed = view.main_embed
        view.current_view_type = "main"
        view.current_category = None
        await view.update_view(interaction)


class BackToCategoriesButton(Button):
    """Button to return to category selection"""
    
    def __init__(self):
        super().__init__(
            style=ButtonStyle.secondary,
            emoji=emojis.LEFT,
            label="Back"
        )
    
    async def callback(self, interaction: discord.Interaction):
        view: CategoryView = self.view
        parent_view = view.parent_view
        
        # Return to main help with category dropdown
        parent_view.current_embed = parent_view.main_embed
        parent_view.current_view_type = "main"
        parent_view.current_category = None
        await parent_view.update_view(interaction)


class DeleteButton(Button):
    """Button to delete the help message"""
    
    def __init__(self):
        super().__init__(
            style=ButtonStyle.danger,
            emoji=emojis.CANCEL,
            label="Delete"
        )
    
    async def callback(self, interaction: discord.Interaction):
        try:
            await interaction.message.delete()
        except discord.NotFound:
            pass
        except discord.Forbidden:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.ERROR,
                    description=f"{emojis.DENY} I don't have permission to delete this message!"
                ),
                ephemeral=True
            )


class PreviousPageButton(Button):
    """Button for previous page in category view"""
    
    def __init__(self):
        super().__init__(
            style=ButtonStyle.primary,
            emoji=emojis.LEFT
        )
    
    async def callback(self, interaction: discord.Interaction):
        view: CategoryView = self.view
        
        if view.current_page > 0:
            view.current_page -= 1
        else:
            view.current_page = view.total_pages - 1
        
        await self.update_page(interaction, view)
    
    async def update_page(self, interaction: discord.Interaction, view: CategoryView):
        """Update the embed with the current page"""
        start_idx = view.current_page * 10
        end_idx = min(start_idx + 10, len(view.command_list))
        page_commands = view.command_list[start_idx:end_idx]
        
        cog_name = view.cog.qualified_name if hasattr(view.cog, 'qualified_name') else str(view.cog)
        emoji = view.parent_view.get_cog_emoji(cog_name)
        
        embed = Embed(
            color=colors.NEUTRAL,
            title=f"{emoji} {cog_name.title()} Commands",
            description=getattr(view.cog, 'description', f"Commands in the {cog_name} category")
        )
        
        embed.set_author(
            name=view.ctx.bot.user.name,
            icon_url=view.ctx.bot.user.avatar.url if view.ctx.bot.user.avatar else view.ctx.bot.user.default_avatar.url
        )
        
        embed.add_field(
            name=f"Commands (Page {view.current_page + 1}/{view.total_pages})",
            value="\n".join(page_commands),
            inline=False
        )
        
        embed.add_field(
            name="Usage",
            value=f"Use `{view.ctx.clean_prefix}help <command>` for detailed help on a specific command.",
            inline=False
        )
        
        embed.set_footer(
            text=f"Requested by {view.ctx.author.display_name} • {len(view.commands)} commands",
            icon_url=view.ctx.author.avatar.url if view.ctx.author.avatar else view.ctx.author.default_avatar.url
        )
        
        await interaction.response.edit_message(embed=embed, view=view)


class NextPageButton(Button):
    """Button for next page in category view"""

    def __init__(self):
        super().__init__(
            style=ButtonStyle.primary,
            emoji=emojis.RIGHT
        )

    async def callback(self, interaction: discord.Interaction):
        view: CategoryView = self.view

        if view.current_page < view.total_pages - 1:
            view.current_page += 1
        else:
            view.current_page = 0

        # Use the same update_page method from PreviousPageButton
        prev_button = PreviousPageButton()
        await prev_button.update_page(interaction, view)


class CommandSearchModal(discord.ui.Modal):
    """Modal for searching specific commands"""

    def __init__(self, ctx: EvelinaContext, mapping: Mapping[Cog | None, List[Command[Any, Callable[..., Any], Any]]]):
        super().__init__(title="Search Commands")
        self.ctx = ctx
        self.mapping = mapping

        self.command_input = discord.ui.TextInput(
            label="Command Name",
            placeholder="Enter command name to search...",
            required=True,
            max_length=50
        )
        self.add_item(self.command_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle command search submission"""
        query = self.command_input.value.lower().strip()

        # Search for matching commands
        matching_commands = []
        for _, commands in self.mapping.items():
            for cmd in commands:
                if cmd.hidden:
                    continue

                # Check permissions
                cog_name = cmd.cog_name
                if cog_name and cog_name.lower() in ["owner", "jishaku", "auth", "helper"]:
                    if self.ctx.author.id not in self.ctx.bot.owner_ids:
                        continue

                # Check if command matches query
                if (query in cmd.qualified_name.lower() or
                    any(query in alias.lower() for alias in cmd.aliases) or
                    (cmd.description and query in cmd.description.lower()) or
                    (cmd.help and query in cmd.help.lower())):
                    matching_commands.append(cmd)

        if not matching_commands:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} No commands found matching `{query}`"
                ),
                ephemeral=True
            )
            return

        # Create search results embed
        embed = Embed(
            color=colors.NEUTRAL,
            title=f"🔍 Search Results for '{query}'",
            description=f"Found {len(matching_commands)} matching command{'s' if len(matching_commands) != 1 else ''}"
        )

        embed.set_author(
            name=self.ctx.bot.user.name,
            icon_url=self.ctx.bot.user.avatar.url if self.ctx.bot.user.avatar else self.ctx.bot.user.default_avatar.url
        )

        # Add matching commands
        command_list = []
        for cmd in sorted(matching_commands[:10], key=lambda x: x.qualified_name.lower()):  # Limit to 10 results
            cmd_desc = cmd.description or cmd.help or "No description"
            if len(cmd_desc) > 50:
                cmd_desc = cmd_desc[:47] + "..."
            command_list.append(f"`{self.ctx.clean_prefix}{cmd.qualified_name}` - {cmd_desc}")

        embed.add_field(
            name="Matching Commands",
            value="\n".join(command_list),
            inline=False
        )

        if len(matching_commands) > 10:
            embed.add_field(
                name="Note",
                value=f"Showing first 10 results. {len(matching_commands) - 10} more commands match your search.",
                inline=False
            )

        embed.add_field(
            name="Usage",
            value=f"Use `{self.ctx.clean_prefix}help <command>` for detailed help on a specific command.",
            inline=False
        )

        embed.set_footer(
            text=f"Requested by {self.ctx.author.display_name}",
            icon_url=self.ctx.author.avatar.url if self.ctx.author.avatar else self.ctx.author.default_avatar.url
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)


class SearchButton(Button):
    """Button to open command search modal"""

    def __init__(self, ctx: EvelinaContext, mapping: Mapping[Cog | None, List[Command[Any, Callable[..., Any], Any]]]):
        super().__init__(
            style=ButtonStyle.secondary,
            emoji="🔍",
            label="Search"
        )
        self.ctx = ctx
        self.mapping = mapping

    async def callback(self, interaction: discord.Interaction):
        """Open search modal"""
        modal = CommandSearchModal(self.ctx, self.mapping)
        await interaction.response.send_modal(modal)


class GroupCommandSelect(Select):
    """Dropdown menu for selecting subcommands in a group"""
    
    def __init__(self, ctx: EvelinaContext, group: Group, commands: List[Command]):
        self.ctx = ctx
        self.group = group
        self.commands = commands
        
        # Create options for each subcommand
        options = []
        
        # Add overview option
        options.append(SelectOption(
            label=f"📋 {group.qualified_name} Overview",
            value="overview",
            description=f"View overview of all {len(commands)} subcommands",
            emoji="📋"
        ))
        
        # Add options for each subcommand
        for cmd in sorted(commands, key=lambda x: x.qualified_name.lower()):
            cmd_desc = cmd.description or cmd.help or "No description"
            if len(cmd_desc) > 100:
                cmd_desc = cmd_desc[:97] + "..."
            
            options.append(SelectOption(
                label=cmd.qualified_name,
                value=f"cmd_{cmd.qualified_name}",
                description=cmd_desc,
                emoji="⚡"
            ))
        
        # Limit to 25 options (Discord limit)
        if len(options) > 25:
            options = options[:25]
        
        super().__init__(
            placeholder=f"📂 Select a {group.qualified_name} subcommand...",
            options=options,
            min_values=1,
            max_values=1
        )
    
    async def callback(self, interaction: discord.Interaction):
        """Handle subcommand selection"""
        view: GroupHelpView = self.view
        selected_value = self.values[0]
        
        if selected_value == "overview":
            # Show overview of all subcommands
            await self.show_overview(interaction, view)
            return
        
        # Extract command name from value
        cmd_name = selected_value.replace("cmd_", "")
        
        # Find the selected command
        selected_cmd = None
        for cmd in self.commands:
            if cmd.qualified_name == cmd_name:
                selected_cmd = cmd
                break
        
        if not selected_cmd:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.ERROR,
                    description=f"{emojis.DENY} Subcommand not found!"
                ),
                ephemeral=True
            )
            return
        
        # Show detailed help for the selected command
        await self.show_command_detail(interaction, view, selected_cmd)
    
    async def show_overview(self, interaction: discord.Interaction, view: 'GroupHelpView'):
        """Show overview of all subcommands in the group"""
        embed = Embed(
            color=colors.NEUTRAL,
            title=f"📋 {self.group.qualified_name} Overview",
            description=self.group.description if self.group.description else self.group.help
        )
        
        embed.set_author(
            name=self.ctx.bot.user.name,
            icon_url=self.ctx.bot.user.avatar.url if self.ctx.bot.user.avatar else self.ctx.bot.user.default_avatar.url
        )
        
        # Add subcommands list
        command_list = []
        for cmd in sorted(self.commands, key=lambda x: x.qualified_name.lower()):
            cmd_desc = cmd.description or cmd.help or "No description"
            if len(cmd_desc) > 50:
                cmd_desc = cmd_desc[:47] + "..."
            command_list.append(f"`{cmd.qualified_name}` - {cmd_desc}")
        
        embed.add_field(
            name=f"Subcommands ({len(self.commands)})",
            value="\n".join(command_list),
            inline=False
        )
        
        embed.add_field(
            name="Usage",
            value=f"Use the dropdown menu to view detailed help for specific subcommands.",
            inline=False
        )
        
        embed.set_footer(
            text=f"Requested by {self.ctx.author.display_name} • {len(self.commands)} subcommands",
            icon_url=self.ctx.author.avatar.url if self.ctx.author.avatar else self.ctx.author.default_avatar.url
        )
        
        view.current_embed = embed
        await view.update_view(interaction, embed)
    
    async def show_command_detail(self, interaction: discord.Interaction, view: 'GroupHelpView', command: Command):
        """Show detailed help for a specific subcommand"""
        def format_cooldown(cooldown):
            cooldown_str = str(cooldown)
            match = re.search(r'per:\s*(\d+(\.\d+)?)', cooldown_str)
            if match:
                seconds = float(match.group(1))
                return f"{int(seconds)} seconds" if seconds.is_integer() else f"{seconds} seconds"
            return "N/A"
        
        params = list(command.clean_params.keys())
        if command.extras:
            params.append("flags")
        
        embed = Embed(
            color=colors.NEUTRAL,
            title=f"Command: {command.qualified_name}",
            description=command.description if command.description else command.help
        )
        
        embed.set_author(
            name=self.ctx.bot.user.name,
            icon_url=self.ctx.bot.user.avatar.url if self.ctx.bot.user.avatar else self.ctx.bot.user.default_avatar.url
        )
        
        embed.add_field(
            name="Aliases",
            value=f"{', '.join(a for a in command.aliases) if len(command.aliases) > 0 else 'N/A'}",
            inline=True
        )
        
        embed.add_field(
            name="Parameters",
            value=', '.join(params) if params else 'N/A',
            inline=True
        )
        
        if command.cooldown and command.brief:
            information = f"{emojis.COOLDOWN} {format_cooldown(command.cooldown)}\n{emojis.WARNING} {command.brief.title()}"
        elif command.cooldown:
            information = f"{emojis.COOLDOWN} {format_cooldown(command.cooldown)}"
        elif command.brief:
            information = f"{emojis.WARNING} {command.brief.title()}"
        else:
            information = "N/A"
        
        embed.add_field(name="Information", value=information, inline=True)
        
        flags = []
        for flag, desc in command.extras.items():
            flags.append(f"`--{flag}`: {desc}")
        if flags:
            embed.add_field(name="Optional Flags", value="\n".join(flags), inline=False)
        
        embed.add_field(
            name="Usage",
            value=f"""```js\nSyntax: {self.ctx.clean_prefix}{command.qualified_name} {' '.join([f'[{a}]' for a in command.clean_params]) if command.clean_params else ''}{' [flags]' if command.extras else ''}\n{f'Example: {self.ctx.clean_prefix}{command.usage}' if command.usage else ''}\n```""",
            inline=False
        )
        
        embed.set_footer(
            text=f"Module: {command.cog_name.lower() + '.py' if command.cog_name else 'N/A'}",
            icon_url=self.ctx.author.avatar.url if self.ctx.author.avatar else self.ctx.author.default_avatar.url
        )
        
        view.current_embed = embed
        await view.update_view(interaction, embed)


class CommandHelpView(View):
    """Enhanced view for individual command help with related commands dropdown"""

    def __init__(self, ctx: EvelinaContext, command: Command, main_embed: Embed):
        super().__init__(timeout=300)
        self.ctx = ctx
        self.command = command
        self.main_embed = main_embed
        self.current_embed = main_embed

        # Get related commands from the same cog
        related_commands = self.get_related_commands()

        # Only add interactive elements if there are related commands (meaning it's part of a group/category)
        if related_commands:
            # Add related commands dropdown
            self.add_item(RelatedCommandsSelect(ctx, command, related_commands))
            # Add category navigation button
            self.add_item(CategoryButton(ctx, command))

        # Always add delete button
        self.add_item(DeleteButton())

    def get_related_commands(self) -> List[Command]:
        """Get other commands from the same cog"""
        if not self.command.cog:
            return []

        related = []
        for cmd in self.command.cog.get_commands():
            if (cmd != self.command and
                not cmd.hidden and
                not (self.command.cog_name and
                     self.command.cog_name.lower() in ["owner", "jishaku", "auth", "helper"] and
                     self.ctx.author.id not in self.ctx.bot.owner_ids)):
                related.append(cmd)

        return sorted(related, key=lambda x: x.qualified_name.lower())[:24]  # Discord limit

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if the user can interact with this view"""
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} {interaction.user.mention}: You are not the **author** of this help menu"
                ),
                ephemeral=True
            )
            return False
        return True

    async def update_view(self, interaction: discord.Interaction, embed: Embed = None, view: View = None):
        """Update the message with new embed and view"""
        if embed is None:
            embed = self.current_embed
        if view is None:
            view = self

        try:
            await interaction.response.edit_message(embed=embed, view=view)
        except discord.InteractionResponded:
            await interaction.edit_original_response(embed=embed, view=view)


class RelatedCommandsSelect(Select):
    """Dropdown menu for selecting related commands from the same category"""

    def __init__(self, ctx: EvelinaContext, current_command: Command, related_commands: List[Command]):
        self.ctx = ctx
        self.current_command = current_command
        self.related_commands = related_commands

        # Create options for each related command
        options = []

        # Add current command option (to return to it)
        options.append(SelectOption(
            label=f"📍 {current_command.qualified_name}",
            value="current",
            description="Currently viewing this command",
            emoji="📍"
        ))

        # Add options for related commands
        for cmd in related_commands:
            cmd_desc = cmd.description or cmd.help or "No description"
            if len(cmd_desc) > 100:
                cmd_desc = cmd_desc[:97] + "..."

            options.append(SelectOption(
                label=cmd.qualified_name,
                value=f"cmd_{cmd.qualified_name}",
                description=cmd_desc,
                emoji="⚡"
            ))

        super().__init__(
            placeholder="🔗 View related commands from this category...",
            options=options,
            min_values=1,
            max_values=1
        )

    async def callback(self, interaction: discord.Interaction):
        """Handle related command selection"""
        view: CommandHelpView = self.view
        selected_value = self.values[0]

        if selected_value == "current":
            # Return to current command
            await view.update_view(interaction, view.main_embed)
            return

        # Extract command name from value
        cmd_name = selected_value.replace("cmd_", "")

        # Find the selected command
        selected_cmd = None
        for cmd in self.related_commands:
            if cmd.qualified_name == cmd_name:
                selected_cmd = cmd
                break

        if not selected_cmd:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.ERROR,
                    description=f"{emojis.DENY} Command not found!"
                ),
                ephemeral=True
            )
            return

        # Show the selected command help
        await self.show_command_help(interaction, view, selected_cmd)

    async def show_command_help(self, interaction: discord.Interaction, view: 'CommandHelpView', command: Command):
        """Show detailed help for the selected command"""
        def format_cooldown(cooldown):
            cooldown_str = str(cooldown)
            match = re.search(r'per:\s*(\d+(\.\d+)?)', cooldown_str)
            if match:
                seconds = float(match.group(1))
                return f"{int(seconds)} seconds" if seconds.is_integer() else f"{seconds} seconds"
            return "N/A"

        params = list(command.clean_params.keys())
        if command.extras:
            params.append("flags")

        embed = Embed(
            color=colors.NEUTRAL,
            title=f"⚡ Command: {command.qualified_name}",
            description=command.description if command.description else command.help
        )

        embed.set_author(
            name=self.ctx.bot.user.name,
            icon_url=self.ctx.bot.user.avatar.url if self.ctx.bot.user.avatar else self.ctx.bot.user.default_avatar.url
        )

        embed.add_field(
            name="Aliases",
            value=f"{', '.join(a for a in command.aliases) if len(command.aliases) > 0 else 'N/A'}",
            inline=True
        )

        embed.add_field(
            name="Parameters",
            value=', '.join(params) if params else 'N/A',
            inline=True
        )

        if command.cooldown and command.brief:
            information = f"{emojis.COOLDOWN} {format_cooldown(command.cooldown)}\n{emojis.WARNING} {command.brief.title()}"
        elif command.cooldown:
            information = f"{emojis.COOLDOWN} {format_cooldown(command.cooldown)}"
        elif command.brief:
            information = f"{emojis.WARNING} {command.brief.title()}"
        else:
            information = "N/A"

        embed.add_field(name="Information", value=information, inline=True)

        flags = []
        for flag, desc in command.extras.items():
            flags.append(f"`--{flag}`: {desc}")
        if flags:
            embed.add_field(name="Optional Flags", value="\n".join(flags), inline=False)

        embed.add_field(
            name="Usage",
            value=f"""```js\nSyntax: {self.ctx.clean_prefix}{command.qualified_name} {' '.join([f'[{a}]' for a in command.clean_params]) if command.clean_params else ''}{' [flags]' if command.extras else ''}\n{f'Example: {self.ctx.clean_prefix}{command.usage}' if command.usage else ''}\n```""",
            inline=False
        )

        # Add category info
        cog_name = command.cog_name if command.cog_name else "N/A"
        embed.set_footer(
            text=f"Requested by {self.ctx.author.display_name} • Module: {cog_name.lower()}.py",
            icon_url=self.ctx.author.avatar.url if self.ctx.author.avatar else self.ctx.author.default_avatar.url
        )

        await interaction.response.edit_message(embed=embed, view=view)


class CategoryButton(Button):
    """Button to view the category that contains this command"""

    def __init__(self, ctx: EvelinaContext, command: Command):
        super().__init__(
            style=ButtonStyle.secondary,
            emoji="📂",
            label="Category"
        )
        self.ctx = ctx
        self.command = command

    async def callback(self, interaction: discord.Interaction):
        """Show the category that contains this command"""
        if not self.command.cog:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} This command is not part of any category!"
                ),
                ephemeral=True
            )
            return

        # Get all commands for the mapping
        mapping = {}
        for cog_name, cog in self.ctx.bot.cogs.items():
            commands = cog.get_commands()
            if commands:
                mapping[cog] = commands

        # Create a mock view for the category display
        mock_view = type('MockView', (), {
            'ctx': self.ctx,
            'mapping': mapping,
            'get_cog_emoji': lambda x: "📁",
            'update_view': lambda interaction, embed, view=None: interaction.response.edit_message(embed=embed, view=view)
        })()

        # Show the category
        category_select = CategorySelect(self.ctx, mapping)
        await category_select.show_category(interaction, mock_view, self.command.cog)


class GroupHelpView(View):
    """View for command group help with dropdown selection"""
    
    def __init__(self, ctx: EvelinaContext, group: Group, commands: List[Command], overview_embed: Embed):
        super().__init__(timeout=300)
        self.ctx = ctx
        self.group = group
        self.commands = commands
        self.overview_embed = overview_embed
        self.current_embed = overview_embed
        
        # Add subcommand dropdown
        self.add_item(GroupCommandSelect(ctx, group, commands))
        
        # Add navigation buttons
        self.add_item(HomeButton())
        self.add_item(DeleteButton())
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if the user can interact with this view"""
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                embed=Embed(
                    color=colors.WARNING,
                    description=f"{emojis.WARNING} {interaction.user.mention}: You are not the **author** of this help menu"
                ),
                ephemeral=True
            )
            return False
        return True
    
    async def update_view(self, interaction: discord.Interaction, embed: Embed = None, view: View = None):
        """Update the message with new embed and view"""
        try:
            if embed is None:
                embed = self.current_embed
            if view is None:
                view = self
                
            await interaction.response.edit_message(embed=embed, view=view)
        except discord.InteractionResponded:
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=view)
        except Exception as e:
            await interaction.followup.send(
                embed=Embed(
                    color=colors.ERROR,
                    description=f"{emojis.DENY} An error occurred: {str(e)}"
                ),
                ephemeral=True
            )

