import os
import random
import requests
import humanize

from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime

from discord import Embed, User, Member, Interaction, File
from discord.ext.commands import Cog, Author, command, group, has_guild_permissions

from modules import config
from modules.styles import emojis, colors
from modules.misc.views import MarryView
from modules.evelinabot import Eve<PERSON>
from modules.helpers import EvelinaContext
from modules.predicates import nsfw_channel
from modules.converters import AbleToMarry

class Roleplay(Cog):
    def __init__(self, bot: <PERSON><PERSON>):
        self.bot = bot
        self.marry_color = 0xFF819F

    async def cog_load(self):
        """Create the roleplay_config table when the cog loads"""
        await self.bot.db.execute("""
            CREATE TABLE IF NOT EXISTS roleplay_config (
                guild_id BIGINT PRIMARY KEY,
                roleplay_enabled BOOLEAN DEFAULT FALSE,
                nsfw_enabled BOOLEAN DEFAULT FALSE
            )
        """)

    async def get_roleplay_config(self, guild_id: int):
        """Get roleplay configuration for a guild"""
        config = await self.bot.db.fetchrow("SELECT * FROM roleplay_config WHERE guild_id = $1", guild_id)
        if not config:
            # Insert default config (both disabled)
            await self.bot.db.execute("INSERT INTO roleplay_config (guild_id, roleplay_enabled, nsfw_enabled) VALUES ($1, $2, $3)", guild_id, False, False)
            return {"roleplay_enabled": False, "nsfw_enabled": False}
        return {"roleplay_enabled": config["roleplay_enabled"], "nsfw_enabled": config["nsfw_enabled"]}

    async def check_roleplay_permissions(self, ctx: EvelinaContext, is_nsfw: bool = False):
        """Check if roleplay commands are enabled in this guild"""
        config = await self.get_roleplay_config(ctx.guild.id)

        if not config["roleplay_enabled"]:
            await ctx.send_warning("Roleplay commands are disabled in this server. An administrator can enable them using `,roleplay config`")
            return False

        if is_nsfw and not config["nsfw_enabled"]:
            await ctx.send_warning("NSFW roleplay commands are disabled in this server. An administrator can enable them using `,roleplay config`")
            return False

        return True

    async def get_count(self, author_id, target_id, type) -> str:
        data = await self.bot.db.fetchrow("SELECT count FROM roleplay WHERE user_id = $1 AND target_id = $2 AND type = $3", author_id, target_id, type)
        if data is None:
            await self.bot.db.execute("INSERT INTO roleplay (user_id, target_id, type, count) VALUES ($1, $2, $3, $4)", author_id, target_id, type, 1)
            return "1st"
        else:
            await self.bot.db.execute("UPDATE roleplay SET count = count + 1 WHERE user_id = $1 AND target_id = $2 AND type = $3", author_id, target_id, type)
            count = data["count"] + 1
            suffix = "th" if 11 <= count % 100 <= 13 else {1: "st", 2: "nd", 3: "rd"}.get(count % 10, "th")
            return f"{count}{suffix}"

    @command(name="kiss", usage="kiss comminate", description="Kiss a member")
    async def kiss(self, ctx: EvelinaContext, *, member: Member):
        if not await self.check_roleplay_permissions(ctx):
            return
        if member == ctx.author:
            return await ctx.send_warning(f"You can't kiss yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/kiss?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Aww how cute!* **{ctx.author.name}** kissed **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'kiss')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="lick", aliases=["slurp"], usage="lick comminate", description="Lick a member")
    async def lick(self, ctx: EvelinaContext, *, member: Member):
        if not await self.check_roleplay_permissions(ctx):
            return
        if member == ctx.author:
            return await ctx.send_warning("You can't lick yourself silly")
        res = ["You slurp that mf.", "Lick Lick!", "Slurp!"]
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/lick?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*{random.choice(res)}* **{ctx.author.name}** licked **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'lick')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="fuck", usage="fuck comminate", brief="NSFW Channel", description="Fuck a member")
    @nsfw_channel()
    async def fuck(self, ctx: EvelinaContext, *, member: Member):
        if not await self.check_roleplay_permissions(ctx, is_nsfw=True):
            return
        if member == ctx.author:
            return await ctx.send_warning("You can't fuck yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/fuck?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh fuck!* **{ctx.author.name}** fucked **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'fucked')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="anal", usage="fuck comminate", brief="NSFW Channel", description="Fuck a member anal")
    @nsfw_channel()
    async def anal(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't fuck yourself anal silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/anal?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh fuck!* **{ctx.author.name}** fucked **{member.name}** anal for the **{await self.get_count(ctx.author.id, member.id, 'anal')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="blowjob", usage="blowjob comminate", brief="NSFW Channel", description="Blowjob a member")
    @nsfw_channel()
    async def blowjob(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't blowjob yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/blowjob?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh fuck!* **{ctx.author.name}** gave **{member.name}** a blowjob for the **{await self.get_count(ctx.author.id, member.id, 'blowjob')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="cum", usage="cum comminate", brief="NSFW Channel", description="Cum on a member")
    @nsfw_channel()
    async def cum(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't cum on yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/cum?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh fuck!* **{ctx.author.name}** cum on **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'cum')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="pussylick", usage="pussylick comminate", brief="NSFW Channel", description="Pussylick a member")
    @nsfw_channel()
    async def pussylick(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't pussylick yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/pussylick?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh fuck!* **{ctx.author.name}** pussylicked **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'pussylick')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @group(name="threesome", brief="NSFW Channel", description="Threesome commands", invoke_without_command=True, case_insensitive=True)
    @nsfw_channel()
    async def threesome(self, ctx: EvelinaContext):
        return await ctx.create_pages()
    
    @threesome.command(name="fff", usage="threesome fff comminate visics", brief="NSFW Channel", description="Threesome with only girls")
    @nsfw_channel()
    async def threesome_fff(self, ctx: EvelinaContext, member: Member, partner: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't threesome with yourself silly")
        if partner == ctx.author:
            return await ctx.send_warning("You can't threesome with yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/threesome_fff?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh yeah!* **{ctx.author.name}** started a threesome with **{member.name}** & **{partner.name}**")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @threesome.command(name="ffm", usage="threesome ffm comminate visics", brief="NSFW Channel", description="Threesome with two girls & one boy")
    @nsfw_channel()
    async def threesome_ffm(self, ctx: EvelinaContext, member: Member, partner: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't threesome with yourself silly")
        if partner == ctx.author:
            return await ctx.send_warning("You can't threesome with yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/threesome_ffm?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh yeah!* **{ctx.author.name}** started a threesome with **{member.name}** & **{partner.name}**")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @threesome.command(name="fmm", usage="threesome fmm comminate visics", brief="NSFW Channel", description="Threesome with one girl & two boys")
    @nsfw_channel()
    async def threesome_fmm(self, ctx: EvelinaContext, member: Member, partner: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't threesomew with yourself silly")
        if partner == ctx.author:
            return await ctx.send_warning("You can't threesomew with yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/threesome_fmm?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh yeah!* **{ctx.author.name}** started a threesome with **{member.name}** & **{partner.name}**")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    # Additional NSFW Commands
    @command(name="tittysuck", usage="tittysuck comminate", brief="NSFW Channel", description="Suck a member's titty")
    @nsfw_channel()
    async def tittysuck(self, ctx: EvelinaContext, *, member: Member):
        if not await self.check_roleplay_permissions(ctx, is_nsfw=True):
            return
        if member == ctx.author:
            return await ctx.send_warning("You can't suck your own titty silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/tittysuck?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh yeah!* **{ctx.author.name}** sucked **{member.name}**'s titty for the **{await self.get_count(ctx.author.id, member.id, 'tittysuck')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="hump", usage="hump comminate", brief="NSFW Channel", description="Hump a member")
    @nsfw_channel()
    async def hump(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't hump yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/hump?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh yeah!* **{ctx.author.name}** humped **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'hump')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="footjob", usage="footjob comminate", brief="NSFW Channel", description="Give a member a footjob")
    @nsfw_channel()
    async def footjob(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't give yourself a footjob silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/footjob?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Oh yeah!* **{ctx.author.name}** gave **{member.name}** a footjob for the **{await self.get_count(ctx.author.id, member.id, 'footjob')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="pinch",usage="pinch comminate", description="Pinch a member")
    async def pinch(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't pinch yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/pinch?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"**{ctx.author.name}** pinches **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'pinch')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)
    
    @command(name="cuddle", usage="cuddle comminate", description="Cuddle a member")
    async def cuddle(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't cuddle yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/cuddle?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Aww how cute!* **{ctx.author.name}** cuddles **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'cuddle')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="hug", usage="hug comminate", description="Hug a member")
    async def hug(self, ctx: EvelinaContext, *, member: Member):
        if not await self.check_roleplay_permissions(ctx):
            return
        if member == ctx.author:
            return await ctx.send_warning("You can't hug yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/hug?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Aww how cute!* **{ctx.author.name}** hugged **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'hug')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="pat", usage="pat comminate", description="Pat a member")
    async def pat(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't pat yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/pat?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Aww how cute!* **{ctx.author.name}** pats **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'pat')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="slap", usage="slap comminate", description="Slap a member")
    async def slap(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't slap yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/slap?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"**{ctx.author.name}** slaps **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'slap')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="laugh", description="Start laughing")
    async def laugh(self, ctx: EvelinaContext):
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/laugh?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"**{ctx.author.name}** laughs")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="cry", description="Start crying")
    async def cry(self, ctx: EvelinaContext):
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/cry?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"**{ctx.author.name}** cries")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    # Additional SFW Roleplay Commands
    @command(name="kill", usage="kill comminate", description="Kill a member")
    async def kill(self, ctx: EvelinaContext, *, member: Member):
        if not await self.check_roleplay_permissions(ctx):
            return
        if member == ctx.author:
            return await ctx.send_warning("You can't kill yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/kill?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"**{ctx.author.name}** killed **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'kill')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="stab", usage="stab comminate", description="Stab a member")
    async def stab(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't stab yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/stab?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"**{ctx.author.name}** stabbed **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'stab')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="eat", usage="eat comminate", description="Eat with a member")
    async def eat(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't eat yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/eat?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Yummy!* **{ctx.author.name}** ate with **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'eat')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="goodmorning", aliases=["gm"], usage="goodmorning comminate", description="Say good morning to a member")
    async def goodmorning(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't say good morning to yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/goodmorning?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Good morning!* **{ctx.author.name}** said good morning to **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'goodmorning')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="goodnight", aliases=["gn"], usage="goodnight comminate", description="Say good night to a member")
    async def goodnight(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't say good night to yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/goodnight?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Good night!* **{ctx.author.name}** said good night to **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'goodnight')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="bite", usage="bite comminate", description="Bite a member")
    async def bite(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't bite yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/bite?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Chomp!* **{ctx.author.name}** bit **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'bite')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="poke", usage="poke comminate", description="Poke a member")
    async def poke(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't poke yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/poke?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Poke!* **{ctx.author.name}** poked **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'poke')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="tickle", usage="tickle comminate", description="Tickle a member")
    async def tickle(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't tickle yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/tickle?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Hehe!* **{ctx.author.name}** tickled **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'tickle')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="dance", usage="dance comminate", description="Dance with a member")
    async def dance(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't dance with yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/dance?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Let's dance!* **{ctx.author.name}** danced with **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'dance')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="wave", usage="wave comminate", description="Wave at a member")
    async def wave(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't wave at yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/wave?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*Hello there!* **{ctx.author.name}** waved at **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'wave')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="highfive", aliases=["hi5"], usage="highfive comminate", description="High five a member")
    async def highfive(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("You can't high five yourself silly")
        result = await self.bot.session.get_json(f"https://api.stun.lat/fun/highfive?key=X3pZmLq82VnHYTd6Cr9eAw")
        embed = Embed(color=colors.NEUTRAL, description=f"*High five!* **{ctx.author.name}** high fived **{member.name}** for the **{await self.get_count(ctx.author.id, member.id, 'highfive')}** time")
        embed.set_image(url=result["url"])
        return await ctx.reply(embed=embed)

    @command(name="stfu", usage="stfu comminate", description="Tell a member to shut up")
    async def stfu(self, ctx: EvelinaContext, *, member: Member):
        if member == ctx.author:
            return await ctx.send_warning("Why do you wan't to tell yourself to shut up")
        return await ctx.send(f"{member.mention} shut the fuck up! {emojis.MADGE}")

    @command(name="ship", usage="ship comminate curet", description="Check the ship rate between you and a member")
    async def ship(self, ctx: EvelinaContext, member: Member, partner: Member = None):
        if partner and (partner == member):
            return await ctx.send_warning("You can't ship the same person twice")
        if (member == ctx.author and partner == ctx.author):
            return await ctx.send_warning("You can't ship yourself with yourself")
        if not partner:
            if member == ctx.author:
                return await ctx.send_warning("You can't ship yourself")
            else:
                partner = ctx.author
        ship_percentage = random.randrange(100)
        progress_bar = self.create_progress_bar(ship_percentage)
        image_path = self.create_ship_image(member.avatar.url if member.avatar else member.default_avatar.url, partner.avatar.url if partner.avatar else partner.default_avatar.url, ship_percentage)
        with open(image_path, "rb") as image_file:
            file = File(image_file, filename="ship.png")
            embed = Embed(color=0xFF819F, description=f"**{member.name}** 💞 **{partner.name}**\n**{ship_percentage}%** {progress_bar}")
            embed.set_image(url=f"attachment://ship.png")
            return await ctx.send(embed=embed, file=file)

    def create_progress_bar(self, percentage):
        filled_blocks = percentage // 8
        half_block = 1 if percentage % 8 >= 4 and filled_blocks > 0 else 0
        empty_blocks = 12 - filled_blocks - half_block
        if filled_blocks > 0:
            progress_bar = f"{emojis.FULLLEFT}"
        else:
            progress_bar = f"{emojis.EMPTYLEFT}"
        progress_bar += f"{emojis.FULL}" * filled_blocks  
        if half_block:
            progress_bar += f"{emojis.HALF}"
        progress_bar += f"{emojis.EMPTY}" * empty_blocks  
        progress_bar += f"{emojis.FULLRIGHT}" if filled_blocks + half_block == 13 else f"{emojis.EMPTYRIGHT}"
        return progress_bar

    def create_ship_image(self, member_avatar, partner_avatar, ship_percentage):
        avatar1 = Image.open(BytesIO(requests.get(str(member_avatar)).content)).convert("RGBA")
        avatar2 = Image.open(BytesIO(requests.get(str(partner_avatar)).content)).convert("RGBA")
        avatar_size = (125, 125)
        avatar1 = avatar1.resize(avatar_size, Image.LANCZOS)
        avatar2 = avatar2.resize(avatar_size, Image.LANCZOS)
        mask = Image.new("L", avatar_size, 0)
        draw = ImageDraw.Draw(mask)
        draw.ellipse((0, 0, avatar_size[0], avatar_size[1]), fill=255)
        avatar1.putalpha(mask)
        avatar2.putalpha(mask)
        file_brokenheart = "data/images/brokenheart.png"
        file_heart = "data/images/heart.png"
        heart_path = file_brokenheart if ship_percentage < 50 else file_heart
        heart = Image.open(heart_path).convert("RGBA")
        heart = heart.resize((75, 75), Image.LANCZOS)
        background = Image.new("RGBA", (280, 125), (255, 255, 255, 0))
        background.paste(avatar1, (0, 0), avatar1)
        background.paste(avatar2, (156, 0), avatar2)
        heart_x = (background.width - heart.width) // 2
        heart_y = (background.height - heart.height) // 2
        background.paste(heart, (heart_x, heart_y), heart)
        output_path = "data/images/tmp/ship.png"
        background.save(output_path, format="PNG")
        return output_path

    @command(name="marry", usage="marry comminate", description="Marry a member")
    async def marry(self, ctx: EvelinaContext, *, member: AbleToMarry):
        embed = Embed(color=self.marry_color, description=f"{emojis.HEART} {ctx.author.mention} wants to marry you. Do you accept?")
        view = MarryView(ctx, member)
        view.message = await ctx.reply(content=member.mention, embed=embed, view=view)

    @command(name="marriage", usage="marriage comminate", description="View your marriage or from a given user")
    async def marriage(self, ctx: EvelinaContext, *, user: User = Author):
        check = await self.bot.db.fetchrow("SELECT * FROM marry WHERE $1 IN (author, soulmate)", user.id)
        if check is None:
            return await ctx.send_warning(f"{'You are' if user == ctx.author else f'{user.mention} is'} not **married**")
        partner_id = check[1] if check[1] != user.id else check[0]
        partner = self.bot.get_user(partner_id)
        if partner is None:
            try:
                partner = await self.bot.fetch_user(partner_id)
            except Exception:
                return await ctx.send_warning("Failed to retrieve the partner's information. Please try again later.")
        async with ctx.typing():
            img = Image.open("data/images/marry.jpeg")
            draw = ImageDraw.Draw(img)
            name_font = ImageFont.truetype("data/fonts/name.ttf", 130)
            sign_font = ImageFont.truetype("data/fonts/sign.ttf", 130)
            date_font = ImageFont.truetype("data/fonts/date.ttf", 100)
            today = datetime.fromtimestamp(int(check['time']))
            date = f"Got married on the {today.day}th of {today.strftime('%B')}, In the year {today.year}"
            draw.text((1500, 1470), user.name, fill="#355482", font=name_font)
            draw.text((2900, 1470), partner.name, fill="#355482", font=name_font)
            draw.text((2400, 1750), date, fill="#95918F", font=date_font, anchor="mm")
            draw.text((1500, 2180), user.name, fill="#355482", font=sign_font)
            draw.text((2850, 2180), partner.name, fill="#355482", font=sign_font)
            buffer = BytesIO()
            img.save(buffer, 'PNG')
            buffer.seek(0)
            file = File(buffer, 'marriage.png')
            embed = Embed(color=self.marry_color, description=f"{emojis.HEART} {ctx.author.mention}: {f'{user.mention} is' if user != ctx.author else 'You are'} currently married to <@!{check[1] if check[1] != user.id else check[0]}> since **{self.bot.misc.humanize_date(datetime.fromtimestamp(int(check['time'])))}**")
            embed.set_image(url=f"attachment://marriage.png")
            return await ctx.reply(embed=embed, file=file)

    @command(name="divorce", description="Divorce from your partner")
    async def divorce(self, ctx: EvelinaContext):
        check = await self.bot.db.fetchrow("SELECT * FROM marry WHERE $1 IN (author, soulmate)", ctx.author.id)
        if check is None:
            return await ctx.send_warning("You are not **married**")
        async def button1_callback(interaction: Interaction) -> None:
            user = await self.bot.fetch_user(check["author"] if check["author"] != interaction.user.id else check["soulmate"])
            await interaction.client.db.execute("DELETE FROM marry WHERE $1 IN (author, soulmate)", interaction.user.id)
            embed = Embed(color=colors.SUCCESS, description=f"{emojis.APPROVE} {interaction.user.mention}: Divorced with partner")
            try:
                dm_embed = Embed(color=0xFF819F, description=f"{emojis.BROKENHEART} It seems like your partner **{interaction.user}** decided to divorce :(\n> Your relationship with them lasted **{humanize.precisedelta(datetime.fromtimestamp(int(check['time'])), format=f'%0.0f')}**")
                await user.send(embed=dm_embed)
            except:
                pass
            await interaction.response.edit_message(content=None, embed=embed, view=None)
        async def button2_callback(interaction: Interaction) -> None:
            embed = Embed(color=colors.ERROR, description=f"{emojis.DENY} {interaction.user.mention}: Divorce got canceled")
            await interaction.response.edit_message(content=None, embed=embed, view=None)
        await ctx.confirmation_send(f"{emojis.QUESTION} {ctx.author.mention} are you sure you want to divorce?", button1_callback, button2_callback)

    @command(name="married", usage="married comminate curet", description="Create a marriage certificate")
    async def married(self, ctx: EvelinaContext, member: User, partner: User):
        if partner and (partner == member):
            return await ctx.send_warning("You can't marry the same person twice.")
        if member == ctx.author and partner == ctx.author:
            return await ctx.send_warning("You can't marry yourself with yourself.")
        if not partner:
            if member == ctx.author:
                return await ctx.send_warning("You can't marry yourself.")
            else:
                partner = ctx.author
        
        async with ctx.typing():
            img = Image.open("data/images/marry.jpeg")
            draw = ImageDraw.Draw(img)

            name_font = ImageFont.truetype("data/fonts/name.ttf", 130)
            sign_font = ImageFont.truetype("data/fonts/sign.ttf", 130)
            date_font = ImageFont.truetype("data/fonts/date.ttf", 100)
            
            today = datetime.now()
            date = f"Got married on the {today.day}th of {today.strftime('%B')}, In the year {today.year}"

            draw.text((1500, 1470), member.name, fill="#355482", font=name_font)
            draw.text((2900, 1470), partner.name, fill="#355482", font=name_font)

            draw.text((2400, 1750), date, fill="#95918F", font=date_font, anchor="mm")

            draw.text((1500, 2180), member.name, fill="#355482", font=sign_font)
            draw.text((2850, 2180), partner.name, fill="#355482", font=sign_font)

            buffer = BytesIO()
            img.save(buffer, 'PNG')
            buffer.seek(0)
            
            file = File(buffer, 'marriage.png')
            await ctx.reply(file=file)

    @command(name="edater", description="List all married users")
    async def edater(self, ctx: EvelinaContext):
        rows = await self.bot.db.fetch("SELECT * FROM marry ORDER BY time DESC")
        if not rows:
            return await ctx.send_warning("There are no married users")
        content = []
        for row in rows:
            author = self.bot.get_user(row["author"])
            soulmate = self.bot.get_user(row["soulmate"])
            time = f"<t:{row['time']}:R>"
            if author and soulmate:
                content.append(f"**{author}** & **{soulmate}** - {time}")
        if not content:
            return await ctx.send_warning("There are no married users")
        await ctx.paginate(content, "Married Users", {"name": ctx.guild.name, "icon_url": ctx.guild.icon.url if ctx.guild.icon else None})

    # Roleplay Management Commands
    @group(name="roleplay", aliases=["rp"], description="Roleplay management commands", invoke_without_command=True, case_insensitive=True)
    async def roleplay_group(self, ctx: EvelinaContext):
        """Roleplay management commands"""
        return await ctx.create_pages()

    @roleplay_group.command(name="stats", description="View roleplay statistics")
    async def roleplay_stats(self, ctx: EvelinaContext, user: User = Author):
        """View roleplay statistics for yourself or another user"""
        # Get user's roleplay stats
        user_stats = await self.bot.db.fetch("SELECT type, SUM(count) as total FROM roleplay WHERE user_id = $1 GROUP BY type ORDER BY total DESC", user.id)

        if not user_stats:
            return await ctx.send_warning(f"{'You have' if user == ctx.author else f'{user.mention} has'} no roleplay statistics")

        # Get total actions
        total_actions = sum(stat["total"] for stat in user_stats)

        # Get top targets
        top_targets = await self.bot.db.fetch("""
            SELECT target_id, SUM(count) as total
            FROM roleplay
            WHERE user_id = $1
            GROUP BY target_id
            ORDER BY total DESC
            LIMIT 5
        """, user.id)

        # Build embed
        embed = Embed(
            color=colors.NEUTRAL,
            title=f"{'Your' if user == ctx.author else f'{user.display_name}\'s'} Roleplay Statistics"
        )
        embed.set_thumbnail(url=user.display_avatar.url)

        # Add total actions
        embed.add_field(
            name="📊 Total Actions",
            value=f"**{total_actions:,}** roleplay actions performed",
            inline=False
        )

        # Add action breakdown
        if user_stats:
            action_breakdown = []
            for stat in user_stats[:10]:  # Show top 10 actions
                action_breakdown.append(f"**{stat['type'].title()}**: {stat['total']:,}")

            embed.add_field(
                name="🎭 Action Breakdown",
                value="\n".join(action_breakdown),
                inline=True
            )

        # Add top targets
        if top_targets:
            target_list = []
            for target in top_targets:
                try:
                    target_user = self.bot.get_user(target["target_id"]) or await self.bot.fetch_user(target["target_id"])
                    target_list.append(f"**{target_user.display_name}**: {target['total']:,}")
                except:
                    target_list.append(f"**Unknown User**: {target['total']:,}")

            embed.add_field(
                name="🎯 Top Targets",
                value="\n".join(target_list),
                inline=True
            )

        # Server-wide stats (if requested for current user)
        if user == ctx.author:
            server_total = await self.bot.db.fetchval("SELECT SUM(count) FROM roleplay r JOIN (SELECT DISTINCT user_id FROM roleplay) u ON r.user_id = u.user_id")
            if server_total:
                embed.add_field(
                    name="🌐 Server Statistics",
                    value=f"**{server_total:,}** total roleplay actions across all users",
                    inline=False
                )

        embed.set_footer(text=f"Requested by {ctx.author.display_name}", icon_url=ctx.author.display_avatar.url)
        return await ctx.reply(embed=embed)

    @roleplay_group.command(name="config", brief="manage guild", description="Configure roleplay settings")
    @has_guild_permissions(manage_guild=True)
    async def roleplay_config(self, ctx: EvelinaContext, setting: str = None, value: str = None):
        """Configure roleplay settings for this server"""
        if not setting:
            # Show current configuration
            config = await self.get_roleplay_config(ctx.guild.id)

            embed = Embed(
                color=colors.NEUTRAL,
                title="Roleplay Configuration",
                description="Current roleplay settings for this server"
            )
            embed.set_thumbnail(url=ctx.guild.icon.url if ctx.guild.icon else None)

            embed.add_field(
                name="🎭 Roleplay Commands",
                value=f"**Status**: {'✅ Enabled' if config['roleplay_enabled'] else '❌ Disabled'}\n**Description**: Basic roleplay commands (hug, kiss, slap, etc.)",
                inline=False
            )

            embed.add_field(
                name="🔞 NSFW Roleplay Commands",
                value=f"**Status**: {'✅ Enabled' if config['nsfw_enabled'] else '❌ Disabled'}\n**Description**: Adult roleplay commands (fuck, cum, tittysuck, etc.)",
                inline=False
            )

            embed.add_field(
                name="⚙️ How to Configure",
                value="`,roleplay config roleplay <enable/disable>` - Toggle basic roleplay commands\n`,roleplay config nsfw <enable/disable>` - Toggle NSFW roleplay commands",
                inline=False
            )

            embed.set_footer(text="Both settings are disabled by default for new servers")
            return await ctx.reply(embed=embed)

        # Handle configuration changes
        setting = setting.lower()
        if setting not in ["roleplay", "nsfw"]:
            return await ctx.send_warning("Invalid setting. Use `roleplay` or `nsfw`")

        if not value:
            return await ctx.send_warning("Please specify `enable` or `disable`")

        value = value.lower()
        if value not in ["enable", "disable"]:
            return await ctx.send_warning("Invalid value. Use `enable` or `disable`")

        enabled = value == "enable"

        # Update configuration
        if setting == "roleplay":
            await self.bot.db.execute("""
                INSERT INTO roleplay_config (guild_id, roleplay_enabled, nsfw_enabled)
                VALUES ($1, $2, FALSE)
                ON CONFLICT (guild_id)
                DO UPDATE SET roleplay_enabled = $2
            """, ctx.guild.id, enabled)

            action = "enabled" if enabled else "disabled"
            return await ctx.send_success(f"Roleplay commands have been **{action}** for this server")

        elif setting == "nsfw":
            await self.bot.db.execute("""
                INSERT INTO roleplay_config (guild_id, roleplay_enabled, nsfw_enabled)
                VALUES ($1, FALSE, $2)
                ON CONFLICT (guild_id)
                DO UPDATE SET nsfw_enabled = $2
            """, ctx.guild.id, enabled)

            action = "enabled" if enabled else "disabled"
            return await ctx.send_success(f"NSFW roleplay commands have been **{action}** for this server")

async def setup(bot: Evelina) -> None:
    return await bot.add_cog(Roleplay(bot))